package com.shuyun.fast.event.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.param.coupon.CouponProjectGetParam;
import com.shuyun.fast.youzan.config.YouZanOpenApiProperties;
import com.shuyun.fast.youzan.coupon.event.YouZanCouponProjectSyncEvent;
import com.shuyun.fast.youzan.coupon.request.*;
import com.shuyun.fast.youzan.service.YouZanOpenApiService;
import com.shuyun.pip.util.UUIDUtils;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import io.micronaut.messaging.Acknowledgement;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Singleton
public class ConsumerProjectInfoListener {

    // 查询商品和店铺信息每批次数量
    private final static int LIMIT_NUM = 1000;
    private final static DateTimeFormatter dateTimeStr = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final static DateTimeFormatter timeStr = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    @Inject
    private BenefitService benefitService;

    @Inject
    private YouZanOpenApiProperties youZanOpenApiProperties;

    @Inject
    private YouZanOpenApiService youZanOpenApiService;

    @KafkaListener(groupId = "consumer-project-info-create", offsetReset = OffsetReset.LATEST)
    @Topic(value = ModelTags.EVENT_TOPIC_PROJECT_CREATE)
    public void projectCreateEvent(YouZanCouponProjectSyncEvent event, Acknowledgement acknowledgement){
        log.info("projectCreateEvent开始=====>project:{}, event:{}", event.getProjectId(), JSONObject.toJSONString(event));
        long startTime = System.currentTimeMillis();
        CouponProject couponProject = getCouponProjectDetail(event.getProjectId());
        if (couponProject == null) {
            log.error("卡券项目:{}不存在，请检查参数是否正确", event.getProjectId());
            return;
        }
        // 判断卡券项目信息发放或核销平台是否包含有赞平台
        if (couponProject.getGrantRestrict().getPlatformsRef().contains("YOUZAN")
                || couponProject.getUseRestrict().getPlatformsRef().contains("YOUZAN")){
            // 判断卡券项目是否为: 满件阶梯抵扣 类型券
            if ("DISCOUNT".equalsIgnoreCase(couponProject.getType().getCode())
                    && "AMOUNT_OFF".equalsIgnoreCase(couponProject.getDiscount().getMode().getCode())
                    && "QUANTITY_STEPPED".equalsIgnoreCase(couponProject.getDiscount().getAmount().getType().getCode())) {
                log.info("卡券项目:{}为抵扣券中的:满件阶梯抵扣 类型券,有赞平台不支持该类型优惠券,无需同步给有赞平台!", couponProject.getId());
                return;
            }
            CouponActivityCreateRequest createRequest = converCouponActivityCreateRequest(couponProject);
            log.info("卡券项目:{}同步创建有赞优惠券活动,参数封装完成.", createRequest.getOutActivityId());
            Long yzCouponProductId = null;
            // 判断是否有限制店铺信息
            if (createRequest.getCardUsingRule().getApplicableShopRangeType() != 3) {
                if (createRequest.getCardUsingRule().getApplicableShopIds().isEmpty()) {
                    log.warn("卡券项目:{}指定有赞店铺信息为空,无法同步有赞平台!", createRequest.getOutActivityId());
                    return;
                }
            }
            // 判断核销平台是否包含有赞平台,不包含有赞,则特殊处理
            if (couponProject.getUseRestrict().getPlatformsRef().contains("YOUZAN")){
                // 判断是否有限制商品信息
                if ("EXCHANGE".equalsIgnoreCase(couponProject.getType().getCode())) {
                    if (createRequest.getPreferentialRule() == null) {
                        log.warn("卡券项目:{}为兑换券,但是兑换有赞商品信息为空,无法同步有赞平台!", couponProject.getId());
                        return;
                    }
                } else {
                    if (createRequest.getCardUsingRule().getApplicableOnlineGoodsRangeType() != 1) {
                        // 判断卡券项目是否设置有赞品牌商品分组信息
                        if (couponProject.getExtData().get("youzanProGroup") != null && StringUtils.isNotBlank(couponProject.getExtData().get("youzanProGroup")+"")){
                            log.info("卡券项目:{}指定有赞品牌商品分组:{}", couponProject.getId(), couponProject.getExtData().get("youzanProGroup"));
                            String[] youzanProGroup = String.valueOf(couponProject.getExtData().get("youzanProGroup")).split(",");
                            List<Long> groupIdList = Arrays.stream(youzanProGroup).map(Long::valueOf).collect(Collectors.toList());
                            // 将商品信息置空,上传商品分组信息
                            createRequest.getCardUsingRule().setApplicableOnlineGoodsIds(null);
                            createRequest.getCardUsingRule().setApplicableOfflineGoodsGroupIds(groupIdList);
                        } else {
                            if (createRequest.getCardUsingRule().getApplicableOnlineGoodsIds().isEmpty()) {
                                log.warn("卡券项目:{}指定有赞商品信息为空,无法同步有赞平台!", couponProject.getId());
                                return;
                            }
                            List<Long> goodsList = createRequest.getCardUsingRule().getApplicableOnlineGoodsIds();
                            log.info("卡券项目:{}指定/排除有赞商品信息共:{}条", couponProject.getId(), goodsList.size());
                            // 创建商品组信息
                            ProductGroupRequest createGroup = new ProductGroupRequest();
                            createGroup.setName(createRequest.getOutActivityId() + "_商品组_" + timeStr.format(LocalDateTime.now()));
                            yzCouponProductId = youZanOpenApiService.productGroupCreate(createRequest.getOutActivityId(), createGroup);
                            if (yzCouponProductId != null) {
                                // 上传商品组和商品关联信息
                                ProductGroupRelationRequest relationGroup = new ProductGroupRelationRequest();
                                relationGroup.setItemGroupId(yzCouponProductId);
                                relationGroup.setItemIds(goodsList);
                                String relation = youZanOpenApiService.productGroupRelation(createRequest.getOutActivityId(), relationGroup);
                                if ("SUCCESS".equalsIgnoreCase(relation)) {
                                    // 将商品组信息放入优惠券活动信息中
                                    List<Long> groupIdList = new ArrayList<>(1);
                                    groupIdList.add(yzCouponProductId);
                                    // 将商品信息置空,上传商品分组信息
                                    createRequest.getCardUsingRule().setApplicableOnlineGoodsIds(null);
                                    createRequest.getCardUsingRule().setApplicableOfflineGoodsGroupIds(groupIdList);
                                } else {
                                    log.warn("卡券项目:{}有赞商品组关联商品信息保存失败,同步创建有赞优惠券活动失败!", createRequest.getOutActivityId());
                                    createGroup.setId(yzCouponProductId);
                                    youZanOpenApiService.productGroupDelete(createRequest.getOutActivityId(), createGroup);
                                    return;
                                }
                            } else {
                                log.warn("卡券项目:{}创建有赞商品组失败,同步创建有赞优惠券活动失败!", createRequest.getOutActivityId());
                                return;
                            }
                        }
                    }
                }
            } else {
                log.info("卡券项目:{}核销平台不包含有赞平台,对指定商品做特殊处理.(不限店铺+指定的特殊商品)", createRequest.getOutActivityId());
                // 将指定的商品组信息放入优惠券活动信息中
                List<Long> goodsIdList = new ArrayList<>(1);
                goodsIdList.add(youZanOpenApiProperties.getSpecialProductId());
                if ("EXCHANGE".equalsIgnoreCase(couponProject.getType().getCode())) {
                    // 兑换券不限商品,兑换商品为指定特殊商品
                    createRequest.getCardUsingRule().setApplicableOnlineGoodsRangeType(1);
                    PreferentialRule preferentialRule = new PreferentialRule();
                    preferentialRule.setExchangeableOnlineGoodsIds(goodsIdList);
                    createRequest.setPreferentialRule(preferentialRule);
                } else {
                    createRequest.getCardUsingRule().setApplicableOnlineGoodsRangeType(2);
                    createRequest.getCardUsingRule().setApplicableOnlineGoodsIds(goodsIdList);
                }
            }

            log.info("卡券项目:{}同步创建有赞优惠券活动开始......", createRequest.getOutActivityId());
            Long yzCouponActivityId = youZanOpenApiService.couponActivityCreate(createRequest);
            if (yzCouponActivityId != null) {
                log.info("卡券项目:{}同步创建有赞优惠券活动成功,activityId:{}", createRequest.getOutActivityId(), yzCouponActivityId);
                Map<String, Object> paramMap = new HashMap<>(2);
                paramMap.put("yzCouponActivityId", yzCouponActivityId);
                if (yzCouponProductId != null) {
                    paramMap.put("yzCouponProductId", yzCouponProductId);
                }
                DMLResponse update = dataapiHttpSdk.update(ModelTags.DATA_FQN_OFFER_V3_PROJECT, createRequest.getOutActivityId(), paramMap, false);
                if (update.getIsSuccess()) {
                    log.info("卡券项目:{}对应有赞券活动ID:{}更新保存成功!", createRequest.getOutActivityId(), yzCouponActivityId);
                } else {
                    log.warn("卡券项目:{}对应有赞券活动ID:{}更新保存失败,失败结果:{}", createRequest.getOutActivityId(), yzCouponActivityId, JSONObject.toJSONString(update));
                }
            } else {
                log.warn("卡券项目:{}同步创建有赞优惠券活动失败!", createRequest.getOutActivityId());
                // 如果商品组信息上传成功,则删除商品组信息
                if (yzCouponProductId != null) {
                    log.info("卡券项目:{}同步创建有赞优惠券活动失败, 删除创建的商品组信息yzCouponProductId:{}", createRequest.getOutActivityId(), yzCouponProductId);
                    ProductGroupRequest deleteGroup = new ProductGroupRequest();
                    deleteGroup.setId(yzCouponProductId);
                    youZanOpenApiService.productGroupDelete(createRequest.getOutActivityId(), deleteGroup);
                }
            }
        } else {
            log.info("卡券项目:{}发放&核销不包含有赞平台,所以不用同步创建卡券项目到有赞平台!", event.getProjectId());
        }
        log.info("projectCreateEvent结束=====>project:{} 耗时:{}毫秒", event.getProjectId(), System.currentTimeMillis() - startTime);
        acknowledgement.ack();
    }

    @KafkaListener(groupId = "consumer-project-info-update", offsetReset = OffsetReset.LATEST)
    @Topic(value = ModelTags.EVENT_TOPIC_PROJECT_UPDATE)
    public void projectUpdateEvent(YouZanCouponProjectSyncEvent event,Acknowledgement acknowledgement){
        log.info("projectUpdateEvent开始=====>project:{}, event:{}", event.getProjectId(), JSONObject.toJSONString(event));
        long startTime = System.currentTimeMillis();
        CouponProject couponProject = getCouponProjectDetail(event.getProjectId());
        if (couponProject == null) {
            log.error("卡券项目:{}不存在，请检查参数是否正确", event.getProjectId());
            return;
        }
        if (couponProject.getGrantRestrict().getPlatformsRef().contains("YOUZAN")
                || couponProject.getUseRestrict().getPlatformsRef().contains("YOUZAN")){
            // 判断卡券项目是否为: 满件阶梯抵扣 类型券
            if ("DISCOUNT".equalsIgnoreCase(couponProject.getType().getCode())
                    && "AMOUNT_OFF".equalsIgnoreCase(couponProject.getDiscount().getMode().getCode())
                    && "QUANTITY_STEPPED".equalsIgnoreCase(couponProject.getDiscount().getAmount().getType().getCode())) {
                log.info("卡券项目:{}为抵扣券中的:满件阶梯抵扣 类型券,有赞平台不支持该类型优惠券,无需同步给有赞平台!", couponProject.getId());
                return;
            }
            CouponActivityUpdateRequest updateRequest = converCouponActivityUpdateRequest(couponProject);
            log.info("卡券项目:{}同步编辑有赞优惠券活动,参数封装完成.", updateRequest.getOutActivityId());
            Long yzCouponProductId = null;
            // 判断是否有限制店铺信息
            if (updateRequest.getCardUsingRule().getApplicableShopRangeType() != 3) {
                if (updateRequest.getCardUsingRule().getApplicableShopIds().isEmpty()) {
                    log.warn("卡券项目:{}指定有赞店铺信息为空,无法同步有赞平台!", updateRequest.getOutActivityId());
                    return;
                }
            }
            // 判断核销平台是否包含有赞平台,不包含有赞,则特殊处理
            if (couponProject.getUseRestrict().getPlatformsRef().contains("YOUZAN")){
                // 判断是否有限制商品信息
                if ("EXCHANGE".equalsIgnoreCase(couponProject.getType().getCode())) {
                    if (updateRequest.getPreferentialRule() == null) {
                        log.warn("卡券项目:{}为兑换券,但是兑换有赞商品信息为空,无法同步有赞平台!", couponProject.getId());
                        return;
                    }
                } else {
                    if (updateRequest.getCardUsingRule().getApplicableOnlineGoodsRangeType() != 1) {
                        // 判断卡券项目是否设置有赞品牌商品分组信息
                        if (couponProject.getExtData().get("youzanProGroup") != null && StringUtils.isNotBlank(couponProject.getExtData().get("youzanProGroup")+"")){
                            log.info("卡券项目:{}指定有赞品牌商品分组:{}", couponProject.getId(), couponProject.getExtData().get("youzanProGroup"));
                            String[] youzanProGroup = String.valueOf(couponProject.getExtData().get("youzanProGroup")).split(",");
                            List<Long> groupIdList = Arrays.stream(youzanProGroup).map(Long::valueOf).collect(Collectors.toList());
                            // 将商品信息置空,上传商品分组信息
                            updateRequest.getCardUsingRule().setApplicableOnlineGoodsIds(null);
                            updateRequest.getCardUsingRule().setApplicableOfflineGoodsGroupIds(groupIdList);
                        } else {
                            if (updateRequest.getCardUsingRule().getApplicableOnlineGoodsIds().isEmpty()) {
                                log.warn("卡券项目:{}指定有赞商品信息为空,无法同步有赞平台!", couponProject.getId());
                                return;
                            }
                            List<Long> goodsList = updateRequest.getCardUsingRule().getApplicableOnlineGoodsIds();
                            log.info("卡券项目:{}指定/排除有赞商品信息共:{}条", couponProject.getId(), goodsList.size());
                            // 创建商品组信息
                            ProductGroupRequest createGroup = new ProductGroupRequest();
                            createGroup.setName(updateRequest.getOutActivityId() + "_商品组_" + timeStr.format(LocalDateTime.now()));
                            yzCouponProductId = youZanOpenApiService.productGroupCreate(updateRequest.getOutActivityId(), createGroup);
                            if (yzCouponProductId != null) {
                                // 上传商品组和商品关联信息
                                ProductGroupRelationRequest relationGroup = new ProductGroupRelationRequest();
                                relationGroup.setItemGroupId(yzCouponProductId);
                                relationGroup.setItemIds(goodsList);
                                String relation = youZanOpenApiService.productGroupRelation(updateRequest.getOutActivityId(), relationGroup);
                                if ("SUCCESS".equalsIgnoreCase(relation)) {
                                    // 将商品组信息放入优惠券活动信息中
                                    List<Long> groupIdList = new ArrayList<>(1);
                                    groupIdList.add(yzCouponProductId);
                                    // 将商品信息置空,上传商品分组信息
                                    updateRequest.getCardUsingRule().setApplicableOnlineGoodsIds(null);
                                    updateRequest.getCardUsingRule().setApplicableOfflineGoodsGroupIds(groupIdList);
                                } else {
                                    log.warn("卡券项目:{}有赞商品组关联商品信息保存失败,同步编辑有赞优惠券活动失败!", updateRequest.getOutActivityId());
                                    createGroup.setId(yzCouponProductId);
                                    youZanOpenApiService.productGroupDelete(updateRequest.getOutActivityId(), createGroup);
                                    return;
                                }
                            } else {
                                log.warn("卡券项目:{}创建有赞商品组失败,同步编辑有赞优惠券活动失败!", updateRequest.getOutActivityId());
                                return;
                            }
                        }
                    }
                }
            } else {
                log.info("卡券项目:{}核销平台不包含有赞平台,对指定商品做特殊处理.(不限店铺+指定的特殊商品)", updateRequest.getOutActivityId());
                // 将指定的商品组信息放入优惠券活动信息中
                List<Long> goodsIdList = new ArrayList<>(1);
                goodsIdList.add(youZanOpenApiProperties.getSpecialProductId());
                if ("EXCHANGE".equalsIgnoreCase(couponProject.getType().getCode())) {
                    // 兑换券不限商品,兑换商品为指定特殊商品
                    updateRequest.getCardUsingRule().setApplicableOnlineGoodsRangeType(1);
                    PreferentialRule preferentialRule = new PreferentialRule();
                    preferentialRule.setExchangeableOnlineGoodsIds(goodsIdList);
                    updateRequest.setPreferentialRule(preferentialRule);
                } else {
                    updateRequest.getCardUsingRule().setApplicableOnlineGoodsRangeType(2);
                    updateRequest.getCardUsingRule().setApplicableOnlineGoodsIds(goodsIdList);
                }
            }
            log.info("卡券项目:{}同步编辑有赞优惠券活动开始......", updateRequest.getOutActivityId());
            String result = youZanOpenApiService.couponActivityUpdate(updateRequest);
            if ("SUCCESS".equalsIgnoreCase(result)) {
                log.info("卡券项目:{}同步编辑有赞优惠券活动成功!", updateRequest.getOutActivityId());
                Map<String, Object> paramMap = new HashMap<>(2);
                paramMap.put("yzCouponProductId", yzCouponProductId);
                DMLResponse update = dataapiHttpSdk.update(ModelTags.DATA_FQN_OFFER_V3_PROJECT, updateRequest.getOutActivityId(), paramMap, false);
                if (update.getIsSuccess()) {
                    log.info("卡券项目:{}对应商品组groupId:{}信息更新保存成功!", updateRequest.getOutActivityId(), yzCouponProductId);
                } else {
                    log.warn("卡券项目:{}对应商品组groupId:{}信息更新保存失败,失败结果:{}", updateRequest.getOutActivityId(), yzCouponProductId, JSONObject.toJSONString(update));
                }
                // 如果原来有关联的商品组信息,则删除商品组信息
                if (event.getYzCouponProductId() != null) {
                    log.info("卡券项目:{}同步编辑有赞优惠券活动成功, 删除原来的商品组信息yzCouponProductId:{}", updateRequest.getOutActivityId(), event.getYzCouponProductId());
                    ProductGroupRequest deleteGroup = new ProductGroupRequest();
                    deleteGroup.setId(event.getYzCouponProductId());
                    youZanOpenApiService.productGroupDelete(updateRequest.getOutActivityId(), deleteGroup);
                }
            } else {
                log.warn("卡券项目:{}同步编辑有赞优惠券活动失败!", updateRequest.getOutActivityId());
                // 如果商品组信息上传成功,则删除商品组信息
                if (yzCouponProductId != null) {
                    log.info("卡券项目:{}同步编辑有赞优惠券活动失败, 删除创建的商品组信息yzCouponProductId:{}", updateRequest.getOutActivityId(), yzCouponProductId);
                    ProductGroupRequest deleteGroup = new ProductGroupRequest();
                    deleteGroup.setId(yzCouponProductId);
                    youZanOpenApiService.productGroupDelete(updateRequest.getOutActivityId(), deleteGroup);
                }
            }
        } else {
            log.info("卡券项目:{}发放&核销不包含有赞平台,所以不用同步编辑卡券项目到有赞平台!", event.getProjectId());
        }
        log.info("projectUpdateEvent结束=====>project:{} 耗时:{}毫秒", event.getProjectId(), System.currentTimeMillis() - startTime);
        acknowledgement.ack();
    }

    @KafkaListener(groupId = "consumer-project-info-offline", offsetReset = OffsetReset.LATEST)
    @Topic(value = ModelTags.EVENT_TOPIC_PROJECT_OFFLINE)
    public void projectOfflineEvent(YouZanCouponProjectSyncEvent event,Acknowledgement acknowledgement){
        log.info("projectOfflineEvent开始=====>project:{}, event:{}", event.getProjectId(), JSONObject.toJSONString(event));
        long startTime = System.currentTimeMillis();
        CouponActivityInvalidRequest invalidRequest = new CouponActivityInvalidRequest();
        invalidRequest.setOutActivityId(event.getProjectId());
        log.info("卡券项目:{}同步失效有赞优惠券活动开始......", invalidRequest.getOutActivityId());
        String result = youZanOpenApiService.couponActivityInvalid(invalidRequest);
        if ("SUCCESS".equalsIgnoreCase(result)) {
            log.info("卡券项目:{}同步失效有赞优惠券活动成功!", invalidRequest.getOutActivityId());
        } else {
            log.warn("卡券项目:{}同步失效有赞优惠券活动失败!", invalidRequest.getOutActivityId());
        }
        log.info("projectOfflineEvent结束=====>project:{} 耗时:{}毫秒", event.getProjectId(), System.currentTimeMillis() - startTime);
        acknowledgement.ack();
    }

    private CouponProject getCouponProjectDetail(String projectId) {
        CouponProjectGetParam projectGetParam = new CouponProjectGetParam();
        projectGetParam.setBizCode("CBANNER");
        projectGetParam.setRequestChannel("YOUZAN");
        projectGetParam.setRequestSystem("YOUZAN");
        projectGetParam.setTransactionId(UUIDUtils.generatorUuidWithOutSplit());
        projectGetParam.setProjectId(projectId);
        projectGetParam.setUseCache(false); // 不查缓存信息
        projectGetParam.setIsSelector(true); // 查询填充选择器信息
        log.info("卡券项目:{} 开始查询卡券详情信息......", projectId);
        BenefitProject projectInfo = benefitService.projectGet(projectGetParam);
        if(projectInfo != null && StringUtils.isNotBlank(projectInfo.getId())){
            // log.info("卡券项目:{} 查询卡券详情信息返回结果:{}", projectId, JSONObject.toJSONString(projectInfo));
            CouponProject couponProject = JsonUtil.outPutConvert(projectInfo, CouponProject.class);
            log.info("卡券项目:{}详情信息查询结果:{}", projectId, JSONObject.toJSONString(couponProject));
            //填充选择器数据
            benefitService.fillSelectorDataNoCache(projectGetParam, couponProject, projectInfo);
            // 查询卡券项目信息
            log.info("卡券项目:{}详情,商品/店铺选择器数据填充成功,指定的店铺信息includeShops:{}条, 指定商品信息includeGoods:{}条,兑换商品信息exchangeGoods:{}条", projectId,
                    couponProject.getIncludeShops() == null ? 0 : couponProject.getIncludeShops().size(),
                    couponProject.getIncludeGoods() == null ? 0 : couponProject.getIncludeGoods().size(),
                    couponProject.getExcludeGoods() == null ? 0 : couponProject.getExcludeGoods().size());
            return couponProject;
        } else {
            // 查询卡券项目信息
            log.warn("卡券项目:{} 查询卡券详情信息失败,返回结果:{}", projectId, JSONObject.toJSONString(projectInfo));
        }
        return null;
    }

    private CouponActivityCreateRequest converCouponActivityCreateRequest(CouponProject couponProject) {
        CouponActivityCreateRequest createRequest = new CouponActivityCreateRequest();
        createRequest.setOutActivityId(couponProject.getId());
        createRequest.setTitle(couponProject.getTitle());
        // 类型，1-普通优惠券 2-员⼯内购券
        if ("INTERNAL_PURCHASE".equalsIgnoreCase(couponProject.getType().getCode())){
            createRequest.setType(2);
            createRequest.setActivityType(7);
        } else {
            createRequest.setType(1);
            if ("EXCHANGE".equalsIgnoreCase(couponProject.getType().getCode())) {
                createRequest.setActivityType(13);
            } else {
                createRequest.setActivityType(7);
            }
        }
        createRequest.setDescription(couponProject.getDescription());
        createRequest.setRemark(couponProject.getCampaignTitle());
        createRequest.setBudgetSendTotalQty(couponProject.getMaxQuantity());
        createRequest.setIsSharable(false);
        // 是否允许转增
        createRequest.setIsHandsel(couponProject.getTransferable() == null ? false : couponProject.getTransferable());

        // 活动制券规则<CardGenRule>
        CreateCardGenRule genRule = new CreateCardGenRule();
        if ("EXCHANGE".equalsIgnoreCase(couponProject.getType().getCode())) {
            // 兑换券
            genRule.setPreferentialMode(3);
        } else {
            // 包含内购券(内购券暂时只包含折扣券) 和 普通优惠券(满减和折扣券)
            if ("RATIO_OFF".equalsIgnoreCase(couponProject.getDiscount().getMode().getCode())){
                // 折扣券
                genRule.setPreferentialMode(2);
            } else {
                // 满减券
                genRule.setPreferentialMode(1);
            }
        }
        initCardGenRule(genRule, couponProject);
        createRequest.setCardGenRule(genRule);

        // 活动核销规则<CardUsingRule>
        CardUsingRule usingRule = initCardUsingRule(couponProject);
        createRequest.setCardUsingRule(usingRule);

        // 活动优惠规则<PreferentialRule>
        PreferentialRule preferentialRule = initPreferentialRule(couponProject);
        createRequest.setPreferentialRule(preferentialRule);
        // log.info("卡券项目:{}创建有赞优惠券活动参数:{}", createRequest.getOutActivityId(), JSONObject.toJSONString(createRequest));
        return createRequest;
    }

    private CouponActivityUpdateRequest converCouponActivityUpdateRequest(CouponProject couponProject) {
        CouponActivityUpdateRequest updateRequest = new CouponActivityUpdateRequest();
        updateRequest.setOutActivityId(couponProject.getId());
        updateRequest.setTitle(couponProject.getTitle());
        updateRequest.setDescription(couponProject.getDescription());
        updateRequest.setRemark(couponProject.getCampaignTitle());
        updateRequest.setBudgetSendTotalQty(couponProject.getMaxQuantity());
        updateRequest.setIsSharable(false);
        // 是否允许转增
        updateRequest.setIsHandsel(couponProject.getTransferable() == null ? false : couponProject.getTransferable());

        // 活动制券规则<CardGenRule>
        CardGenRule genRule = new CardGenRule();
        initCardGenRule(genRule, couponProject);
        updateRequest.setCardGenRule(genRule);

        // 活动核销规则<CardUsingRule>
        CardUsingRule usingRule = initCardUsingRule(couponProject);
        updateRequest.setCardUsingRule(usingRule);

        // 活动优惠规则<PreferentialRule>
        PreferentialRule preferentialRule = initPreferentialRule(couponProject);
        updateRequest.setPreferentialRule(preferentialRule);
        //log.info("卡券项目:{}编辑有赞优惠券活动参数:{}", updateRequest.getOutActivityId(), JSONObject.toJSONString(updateRequest));
        return updateRequest;
    }

    private void initCardGenRule(CardGenRule genRule, CouponProject couponProject) {
        // 活动制券规则<CardGenRule>
        if ("EXCHANGE".equalsIgnoreCase(couponProject.getType().getCode())) {
            // 兑换券
            genRule.setVoucherValueGenerateType(4);
            genRule.setValue(0L);
        } else {
            // 包含内购券(内购券暂时只包含折扣券) 和 普通优惠券(满减和折扣券)
            if ("RATIO_OFF".equalsIgnoreCase(couponProject.getDiscount().getMode().getCode())){
                // 折扣率 如8折,需要传:80 (需要 * 100)
                genRule.setValue(couponProject.getDiscount().getAmount().getDiscountValue().multiply(new BigDecimal("100")).longValue());
            } else {
                // 抵扣券, 满减金额(单位:分) 需要 * 100
                // 判断抵扣券是否为固定金额
                if ("FIXED".equalsIgnoreCase(couponProject.getDiscount().getAmount().getType().getCode())){
                    genRule.setValue(couponProject.getDiscount().getAmount().getValue().multiply(new BigDecimal("100")).longValue());
                } else {
                    log.warn("卡券项目:{}为抵扣券中的:满件阶梯抵扣 类型券,没有抵扣金额,无法同步给有赞!", couponProject.getId());
                    genRule.setValue(null);
                }
            }
            genRule.setVoucherValueGenerateType(1);
        }
        // 卡券生效时间设置
        if ("FIXED_TIME".equalsIgnoreCase(couponProject.getExpiredPeriod().getType().getCode())){
            // 绝对时间
            genRule.setValidTimeGenerateType(1);
            // 设置卡券生效时间如果绝对时间为空,则默认为当前时间
            if (couponProject.getEffectPeriod().getFixed() != null){
                genRule.setAbsoluteValidStartTime(dateTimeStr.format(couponProject.getEffectPeriod().getFixed()));
            } else {
                genRule.setAbsoluteValidStartTime(dateTimeStr.format(LocalDateTime.now()));
            }
            // 卡券失效时间
            genRule.setAbsoluteValidEndTime(dateTimeStr.format(couponProject.getExpiredPeriod().getFixed()));
        } else {
            // 相对时间
            genRule.setValidTimeGenerateType(2);
            // 除了卡券生效相对时间设置为天之外的其他情况,默认相对0天后生效,即发放后立即生效
            if ("RELATIVE_TIME".equalsIgnoreCase(couponProject.getEffectPeriod().getType().getCode())
                    && couponProject.getEffectPeriod().getRelatives() != null
                    && !couponProject.getEffectPeriod().getRelatives().isEmpty()
                    && couponProject.getEffectPeriod().getRelatives().get(0).getDuration() != null
                    && couponProject.getEffectPeriod().getRelatives().get(0).getDuration().getDay() != null) {
                genRule.setRelativeValidTimeBeginInterval(couponProject.getEffectPeriod().getRelatives().get(0).getDuration().getDay());
            } else {
                genRule.setRelativeValidTimeBeginInterval(0);
            }
            // 设置卡券有效天数,如果设置非天数或月数,则默认当年有效.
            if ("RELATIVE_TIME".equalsIgnoreCase(couponProject.getExpiredPeriod().getType().getCode())
                    && couponProject.getExpiredPeriod().getRelatives() != null
                    && !couponProject.getExpiredPeriod().getRelatives().isEmpty()
                    && couponProject.getExpiredPeriod().getRelatives().get(0).getDuration() != null
                    && couponProject.getExpiredPeriod().getRelatives().get(0).getDuration().getDay() != null) {
                    genRule.setRelativeValidTimeDuration(couponProject.getExpiredPeriod().getRelatives().get(0).getDuration().getDay());
            } else {
                // 默认有效期1天
                genRule.setRelativeValidTimeDuration(1);
            }
        }
    }

    private CardUsingRule initCardUsingRule(CouponProject couponProject) {
        // 活动核销规则<CardUsingRule>
        CardUsingRule usingRule = new CardUsingRule();
        // 核销店铺信息
        usingRule.setApplicableShopRangeType(3);
        // 指定店铺信息非空,则转换店铺信息
        if (couponProject.getRestrict() != null && couponProject.getRestrict().getShopsRefType() != null
                && "INCLUDE".equalsIgnoreCase(couponProject.getRestrict().getShopsRefType().getCode())){
            if (couponProject.getIncludeShops() != null && !couponProject.getIncludeShops().isEmpty()) {
                List<String> shopIdList = converShopIds(couponProject.getIncludeShops());
                if (shopIdList.isEmpty()) {
                    log.warn("卡券项目:{}指定店铺信息中,有赞店铺信息为空,无法同步卡券项目信息到有赞!", couponProject.getId());
                }
                usingRule.setApplicableShopRangeType(2);
                usingRule.setApplicableShopIds(shopIdList);
            } else {
                log.error("卡券项目:{}选择器中指定店铺信息为空!", couponProject.getId());
            }
        }
        // 如果核销平台包含有赞平台,商品信息正常处理
        if (couponProject.getUseRestrict().getPlatformsRef().contains("YOUZAN")){
            // 如果为兑换券,必须设置为: 1-不限商品
            if ("EXCHANGE".equalsIgnoreCase(couponProject.getType().getCode())) {
                usingRule.setApplicableOnlineGoodsRangeType(1);
            } else {
                // 不限商品
                usingRule.setApplicableOnlineGoodsRangeType(1);
                if (couponProject.getRestrict() != null && couponProject.getRestrict().getGoodsRefType() != null) {
                    if ("INCLUDE".equalsIgnoreCase(couponProject.getRestrict().getGoodsRefType().getCode())){
                        // 指定的商品,如果有固定商品组信息,则不查询指定商品信息
                        if (couponProject.getExtData().get("youzanProGroup") != null && StringUtils.isNotBlank(couponProject.getExtData().get("youzanProGroup")+"")){
                            usingRule.setApplicableOnlineGoodsRangeType(2);
                            log.info("卡券项目:{}指定商品信息中,包含固定商品组信息,无需查询指定商品信息!", couponProject.getId());
                        } else {
                            if (couponProject.getIncludeGoods() != null && !couponProject.getIncludeGoods().isEmpty()) {
                                List<Long> includeGoodsIdList = converGoodsIds(couponProject.getIncludeGoods());
                                if (includeGoodsIdList.isEmpty()) {
                                    log.warn("卡券项目:{}指定商品信息中,有赞商品信息为空,无法同步卡券项目信息到有赞!", couponProject.getId());
                                }
                                usingRule.setApplicableOnlineGoodsRangeType(2);
                                usingRule.setApplicableOnlineGoodsIds(includeGoodsIdList);
                            } else {
                                log.error("卡券项目:{}选择器中指定商品信息为空!", couponProject.getId());
                            }
                        }
                    }
                    if ("EXCLUDE".equalsIgnoreCase(couponProject.getRestrict().getGoodsRefType().getCode())) {
                        // 排除商品
                        if (couponProject.getExcludeGoods() != null && !couponProject.getExcludeGoods().isEmpty()) {
                            List<Long> excludeGoodsIdList = converGoodsIds(couponProject.getExcludeGoods());
                            if (!excludeGoodsIdList.isEmpty()) {
                                usingRule.setApplicableOnlineGoodsRangeType(3);
                                usingRule.setApplicableOnlineGoodsIds(excludeGoodsIdList);
                            } else {
                                log.info("卡券项目:{}排除商品信息中,有赞商品信息为空,不限有赞商品信息!", couponProject.getId());
                            }
                        } else {
                            log.error("卡券项目:{}选择器中排除商品信息为空!", couponProject.getId());
                        }
                    }
                }
            }
        }
        // 门槛限制信息
        if (couponProject.getDiscount() != null && couponProject.getDiscount().getAmount() != null
                && couponProject.getDiscount().getAmount().getThreshold() != null
                && couponProject.getDiscount().getAmount().getThreshold().getValue() != null
                && couponProject.getDiscount().getAmount().getThreshold().getValue().doubleValue() > 0){
            usingRule.setThresholdType(1);
            usingRule.setThresholdAmount(couponProject.getDiscount().getAmount().getThreshold().getValue().multiply(new BigDecimal("100")).longValue());
        } else if(couponProject.getDiscount() != null && couponProject.getDiscount().getAmount() != null
                && couponProject.getDiscount().getAmount().getThreshold() != null
                && couponProject.getDiscount().getAmount().getThreshold().getNum() != null
                && couponProject.getDiscount().getAmount().getThreshold().getNum().doubleValue() > 0){
            usingRule.setThresholdType(2);
            usingRule.setThresholdAmount(couponProject.getDiscount().getAmount().getThreshold().getNum().longValue());
        }else {
            usingRule.setThresholdType(0);
        }
        // 最⼤折扣上限，单位是分 0：代表没有折扣上限(默认值)
        usingRule.setMaxDiscountAmount(0L);
        // 是否禁⽌叠加优惠  true-禁止; false-允许
        if (couponProject.getCombinationType() != null && "NO_OVERLAY".equalsIgnoreCase(couponProject.getCombinationType().getCode())){
            // 禁止叠加
            usingRule.setIsForbidOverlayPreferential(true);
        } else {
            usingRule.setIsForbidOverlayPreferential(false);
        }
        if (couponProject.getExtData().get("minOrderdiscount") != null && StringUtils.isNotBlank(couponProject.getExtData().get("minOrderdiscount")+"")){
            usingRule.setDiscountLimit(new BigDecimal(couponProject.getExtData().get("minOrderdiscount")+"").multiply(new BigDecimal("100")).longValue());
        }
        return usingRule;
    }

    private PreferentialRule initPreferentialRule(CouponProject couponProject) {
        // 活动优惠规则<PreferentialRule>
        if (couponProject.getUseRestrict().getPlatformsRef().contains("YOUZAN")
                && "EXCHANGE".equalsIgnoreCase(couponProject.getType().getCode())) {
            // 兑换商品
            if (couponProject.getExchangeGoods() != null && !couponProject.getExchangeGoods().isEmpty()) {
                List<Long> goodsIdList = converGoodsIds(couponProject.getExchangeGoods());
                if (!goodsIdList.isEmpty()) {
                    PreferentialRule preferentialRule = new PreferentialRule();
                    preferentialRule.setExchangeableOnlineGoodsIds(goodsIdList);
                    return preferentialRule;
                } else {
                    log.warn("卡券项目:{}为兑换券,但是兑换有赞商品信息为空!", couponProject.getId());
                }
            } else {
                log.error("卡券项目:{}为兑换券,选择器中兑换商品信息为空!", couponProject.getId());
            }
        }
        return null;
    }

    private List<String> converShopIds(List<String> shopIdList){
        List<String> result = new ArrayList<>(shopIdList.size());
        if (!shopIdList.isEmpty()) {
            log.info("开始查询有赞店铺编码信息......");
            long start = System.currentTimeMillis();
            // 分批次查询,每批次1000条
            List<List<String>> pageList = pageListIds(shopIdList);
            for (List<String> tempShopIdList : pageList) {
                if (!tempShopIdList.isEmpty()) {
                    StringBuffer shopIds = new StringBuffer();
                    for (String shopId : tempShopIdList) {
                        shopIds.append("'").append(shopId).append("',");
                    }
                    String shopIdStr = shopIds.substring(0, shopIds.length() - 1);
                    String querySql = "SELECT shopCode FROM " + String.format(ModelTags.DATA_FQN_MDM_SHOP, "CBANNER") + " WHERE channelType = 'YOUZAN' AND shopCode IN (" + shopIdStr + ")";
                    BaseResponse response = dataapiHttpSdk.execute(querySql, Collections.emptyMap());
                    if (response.getIsSuccess()) {
                        //log.info("查询SQL执行成功:{}", querySql);
                        List<Map<String, String>> dataList = response.getData();
                        if (dataList != null && !dataList.isEmpty()) {
                            for (Map<String, String> tempMap : dataList) {
                                if (StringUtils.isNotBlank(tempMap.get("shopCode"))) {
                                    result.add(tempMap.get("shopCode"));
                                }
                            }
                        } else {
                            log.info("有赞店铺ID信息查询为空:{}", JSON.toJSONString(response));
                        }
                    } else {
                        log.warn("SQL语句执行失败,返回错误信息:{}", JSON.toJSONString(response));
                    }
                }
            }
            log.info("有赞店铺ID查询结果:{} ", JSONObject.toJSONString(result));
            log.info("有赞店铺ID查询耗时:{} ms", (System.currentTimeMillis() - start));
        }
        return result;
    }

    private List<Long> converGoodsIds(List<String> goodsIdList){
        List<Long> result = new ArrayList<>(goodsIdList.size());
        if (!goodsIdList.isEmpty()) {
            log.info("开始查询有赞商品ID信息......");
            long start = System.currentTimeMillis();
            // 分批次查询,每次查询1000条
            List<List<String>> pageList = pageListIds(goodsIdList);
            for (List<String> tempGoodIdList : pageList) {
                if (!tempGoodIdList.isEmpty()) {
                    StringBuffer goodIds = new StringBuffer();
                    for (String goodId : tempGoodIdList) {
                        goodIds.append("'").append(goodId).append("',");
                    }
                    String goodIdStr = goodIds.substring(0, goodIds.length() - 1);
                    // log.info("拼接后的goodIds参数信息:{}", goodIdStr);
                    // 查询有赞商品ID
                    String querySql = "SELECT productId FROM " + String.format(ModelTags.DATA_FQN_YOUZAN_PRODUCT, "CBANNER") + " WHERE productCode IN (" + goodIdStr + ") AND isValid = 'Y'";
                    BaseResponse response = dataapiHttpSdk.execute(querySql, Collections.emptyMap());
                    if (response.getIsSuccess()) {
                        // log.info("查询SQL执行成功:{}", querySql);
                        List<Map<String, Object>> dataList = response.getData();
                        if (dataList != null && !dataList.isEmpty()) {
                            Long productId;
                            for (Map<String, Object> tempMap : dataList) {
                                if (tempMap.get("productId") != null && StringUtils.isNotBlank(tempMap.get("productId").toString())) {
                                    productId = Long.valueOf(tempMap.get("productId").toString());
                                    if (!result.contains(productId)){
                                        result.add(productId);
                                    }
                                } else {
                                    log.error("有赞商品Id:{} 为空或非纯数字,格式错误!", tempMap.get("productId"));
                                }
                            }
                        }else{
                            log.info("有赞商品ID信息查询为空:{}", JSON.toJSONString(response));
                        }
                    } else {
                        log.warn("SQL语句执行失败,返回错误信息:{}", JSON.toJSONString(response));
                    }
                }
            }
            // log.info("有赞商品ID查询结果:{} ", JSONObject.toJSONString(result));
            log.info("有赞商品ID查询到总条数:{}条,查询耗时:{} ms", result.size(), (System.currentTimeMillis() - start));
        }
        return result;
    }

    private List<List<String>> pageListIds(List<String> idList) {
        List<List<String>> result = new ArrayList<>();
        List<String> temp = new ArrayList<>(LIMIT_NUM);
        int count = 1;
        for (String id : idList) {
            temp.add(id);
            if (count % LIMIT_NUM == 0) {
                result.add(temp);
                temp = new ArrayList<>(LIMIT_NUM);
            }
            count++;
        }
        if (!temp.isEmpty()) {
            result.add(temp);
        }
        return result;
    }
}
