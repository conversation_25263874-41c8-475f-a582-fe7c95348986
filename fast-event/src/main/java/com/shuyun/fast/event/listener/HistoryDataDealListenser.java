package com.shuyun.fast.event.listener;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.client.v1_0_0.EbrandMemberClient;
import com.shuyun.fast.service.v1_0_0.BojunInterfaceService;
import com.shuyun.fast.service.v1_0_0.HisDataDealService;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import io.micronaut.context.annotation.Property;
import io.micronaut.messaging.Acknowledgement;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title HistoryDataDealListenser
 * @description 历史数据处理
 * @create 2024/10/10 17:00
 */
@Singleton
@Slf4j
public class HistoryDataDealListenser {
    @Inject
    private BojunInterfaceService bojunInterfaceService;

    @Inject
    private EbrandMemberClient ebrandMemberClient;

    @Inject
    private HisDataDealService hisDataDealService;
    /*@Inject
    private JdbcTemplate jdbcTemplate;*/

    /**
     * 历史会员注册
     * @param param
     */
    @KafkaListener(groupId = "fast-member-wechat", offsetReset = OffsetReset.LATEST, threads = 24, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = ModelTags.EVENT_TOPIC_HISMEMBER_DEAL)
    public void hisMemberDeal(Map<String,Object> param){
        try {
            JSONObject event = (JSONObject) JSONObject.toJSON(param);
            hisDataDealService.hisMemberRegister(event);
        }catch (Exception e){
            log.error("历史会员注册出现异常",e);

        }

    }


    @KafkaListener(groupId = "consumer-his—wechat-member-deal", offsetReset = OffsetReset.LATEST, threads = 4)
    @Topic(value = ModelTags.EVENT_TOPIC_WECHAT_HISMEMBER_DEAL)
    public void hiswechatMemberDeal(Map<String,Object> param){
        try {
            JSONObject event = (JSONObject) JSONObject.toJSON(param);
            hisDataDealService.hisMemberRegister(event);
        }catch (Exception e){
            log.error("微信历史会员注册出现异常",e);
        }

    }

    @KafkaListener(groupId = "consumer-his-taobao-member-deal", offsetReset = OffsetReset.LATEST, threads = 4)
    @Topic(value = ModelTags.EVENT_TOPIC_TAOBAO_HISMEMBER_DEAL)
    public void hisTaobaoMemberDeal(Map<String,Object> param){
        try {
            JSONObject event = (JSONObject) JSONObject.toJSON(param);
            hisDataDealService.hisMemberRegister(event);
        }catch (Exception e){
            log.error("淘宝历史会员注册出现异常",e);
        }

    }

    @KafkaListener(groupId = "consumer-his-grade-deal", offsetReset = OffsetReset.LATEST, threads = 4)
    @Topic(value = ModelTags.EVENT_TOPICHISGRADE_DEAL)
    public void hisGradeDeal(Map<String,Object> param){
        try {
            JSONObject event = (JSONObject) JSONObject.toJSON(param);
            hisDataDealService.hisGradeDeal(event);
        }catch (Exception e){
            log.error("历史等级初始化出现异常",e);
        }
    }

    @KafkaListener(groupId = "consumer-his-point-deal",  offsetReset = OffsetReset.LATEST, threads = 8, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = ModelTags.EVENT_TOPICHISPOINT_DEAL)
    public void hisPointDeal(Map<String,Object> param){
        try {
            JSONObject event = (JSONObject) JSONObject.toJSON(param);
            log.info("历史会员增量积分初始化:{]",event);
            String action=event.getString("action");
            if("SEND".equals(action)){
                hisDataDealService.hisPointSend(event);
            }else{
                hisDataDealService.hisPointDeduct(event);
            }
        }catch (Exception e){
            log.error("历史积分初始化出现异常",e);
        }
    }

    @KafkaListener(groupId = "consumer-nancent-refund-deal",  offsetReset = OffsetReset.LATEST, threads = 4, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = ModelTags.EVENT_NANCENT_REFUND_DEAL)
    public void nancentRefundDeal(Map<String,Object> param){
        try {
            JSONObject event = (JSONObject) JSONObject.toJSON(param);
            log.info("南讯切换前订单发生退单:{}",event);
            hisDataDealService.hisPointDeduct(event);
        }catch (Exception e){
            log.error("南讯切换前订单退单处理异常",e);
        }
    }


    /**
     * 淘宝历史积分等级同步
     * @param param
     */
/*    @KafkaListener(groupId = "consumer-taobao-sync", offsetReset = OffsetReset.LATEST, threads = 4)
    @Topic(value = ModelTags.EVENT_TOPIC_TAOBAO_SYNC)
    public void taobaoMemberSync(Map<String,Object> param){
        try {
            JSONObject event = (JSONObject) JSONObject.toJSON(param);
            log.info("fast.event.taobao.member.sync:{}",event);
            SyncRequest request = new SyncRequest();
            request.setNick(event.getString("nick"));
            request.setOuid(event.getString("ouid"));
            request.setPoint(event.getLong("point"));
            request.setLevel(event.getLong("level"));
            request.setSellerName(event.getString("sellerName"));
            request.setMixMobile(event.getString("mixMobile"));
            //log.info("调用会籍注册接口入参为：{}",requestParam);
            ebrandMemberClient.taobaoSync(request);
        }catch (Exception e){
            log.error("历史会员同步淘宝出现异常",e);
        }
    }*/

    /**
     * ouid加密
     * @param param
     */
    @KafkaListener(groupId = "consumer-his-member-ouid_deal", offsetReset = OffsetReset.LATEST, threads = 4)
    @Topic(value = ModelTags.EVENT_TOPIC_HISMEMBER_OUID_DEAL)
    public void ouidDeal(Map<String,Object> param){
        try {
            JSONObject event = (JSONObject) JSONObject.toJSON(param);
            log.info("fast.event.history.member.ouid.deal:{}",event);
            hisDataDealService.ouidDeal(event);
        }catch (Exception e){
            log.error("淘宝ouid加密出现异常",e);
        }
    }


    @KafkaListener(groupId = "consumer-his-member-bind_push3", offsetReset = OffsetReset.LATEST, threads = 24, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = ModelTags.EVENT_TOPIC_HISMEMBER_GUIDE_PUSH)
    public void hisMemberGuidePush(Map<String,Object> param){

            JSONObject event = (JSONObject) JSONObject.toJSON(param);
            log.info("fast.event.history.member.ouid.deal:{}",event);
            hisDataDealService.hisMemberGuidePush(event,"/member/bindGuide");

    }

    @KafkaListener(groupId = "consumer-his-member-bind_push4", offsetReset = OffsetReset.LATEST, threads = 24, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = ModelTags.EVENT_TOPIC_HISMEMBER_GUIDE_PUSH4)
    public void hisMemberGuidePush4(Map<String,Object> param){

        JSONObject event = (JSONObject) JSONObject.toJSON(param);
        log.info("fast.event.history.member.guide.push4:{}",event);
        hisDataDealService.hisMemberGuidePush(event,"/member/updateMemberByMemberId");

    }


    /**
     * 会员单推送导购
     * @param param
     */
    @KafkaListener(groupId = "member-order-guide-push02", offsetReset = OffsetReset.LATEST)
    @Topic(value = ModelTags.EVENT_TOPIC_MEMBER_ORDER_TO_GUIDE)
    public void memberOrderToGuide(Map<String,Object> param){
        JSONObject event = (JSONObject) JSONObject.toJSON(param);
        String orderType = event.getString("orderType");
        String orderId = event.getString("orderId");
        String channelType = event.getString("channelType");
        //log.info("fast.event.member.order.guide 开始同步orderType:{} orderId:{} event:{}", orderType, orderId, event);
        try {
            List<Object> objects = new ArrayList<>();
            objects.add(event);
            JSONObject params = new JSONObject();
            params.put("list",objects);
            //log.info("memberOrderToGuide推送消息体:{}",params);
            if ("POS".equalsIgnoreCase(channelType)) {
                hisDataDealService.hisMemberOrderGuidePush(orderType, orderId, params);
            }
        }catch (Exception e){
            log.error("会员单:{}推送导购出现异常:{}", orderId, e);
        }
    }

    @KafkaListener(groupId = "consumer-guide-external-change", offsetReset = OffsetReset.LATEST, threads = 4, properties = @Property(name = "max.poll.records", value = "50"))
    @Topic(value = ModelTags.EVENT_TOPIC_GUIDE_EXTERNAL_CHANGE)
    public void guideExternalChange(Map<String,Object> param, Acknowledgement acknowledgement){
        JSONObject event = (JSONObject) JSONObject.toJSON(param);
        log.info("fast.event.guide.external.change:{}",event);
        hisDataDealService.guideExternalChange(param);
        acknowledgement.ack();
    }


}
